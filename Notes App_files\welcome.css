@import url('reset.css');

:root {
    --na-overlay: hsla(0, 0%, 5%, 0.300);
    --na-highlight: hsl(195, 10%, 70%);
}

@media (prefers-color-scheme: light) {
    :root {
        --na-overlay: hsla(210, 20%, 98%, 0.700);
        --na-highlight: hsl(192, 3%, 40%);
    }
}

.welcome-overlay {
    background-color: var(--color-background-primary);
    width: 100%;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9998;
    transition: opacity 300ms ease, visibility 0.6s ease;
    opacity: 1;
    cursor: url("https://cdn.prod.website-files.com/6708f85ff3d3cba6aff436fb/671251b239d7aeb290a31ac5_cursor-default%402x.svg") 2 0,
        auto;
}

.welcome-overlay.hidden {
    opacity: 0;
    visibility: hidden;
}

.continue-btn {
    background: var(--na-primary);
    color: var(--na-primary-text);
    padding: 0.3rem 1rem;
    font-size: 1.125rem;
    font-weight: 500;
    border-radius: 50px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    letter-spacing: -0.2px;
    cursor: url("https://cdn.prod.website-files.com/6708f85ff3d3cba6aff436fb/671251b212e6b71494aa67ff_cursor-pointer%402x.svg") 12 0,
        pointer;
    position: fixed;

    left: 50%;
    top: 60%;
    transform: translateX(-50%);

    opacity: 0;
    visibility: hidden;
}

.continue-btn:active {
    transform: translateX(-50%) scale(0.95);
}

.continue-btn:focus {
    outline: 2px solid var(--na-highlight);
    outline-offset: 2px;
}

.continue-btn:hover {
    background: var(--na-primary-text);
    color: var(--na-primary);
}

#welcome-animate {
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%) scale(60%);
    min-width: max-content;
    font-size: 3.8vw;
    font-family: 'Gambarino', serif;
    transition: all 1000ms cubic-bezier(0.19, 1, 0.22, 1);
    font-style: normal;
    letter-spacing: -0.1vw;
}

#welcome-animate strong {
    font-weight: 500;
    letter-spacing: -1px;
    color: var(--na-highlight);
    line-height: 0;
    border-radius: 8px;
    font-family: "General Sans", sans-serif;
    text-box-edge: cap;
}

.char {
    will-change: transform;
}

.scramble-overlay-bg {
    background-color: var(--na-overlay);
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.hello-language {
    position: fixed;
    left: 50%;
    top: 50%;
    font-family: 'Gambarino', serif;
    transform: translate(-50%, -50%);
    font-size: 5.25vw;
    font-style: italic;
    opacity: 0;
    letter-spacing: -0.2vw;
    font-weight: 500;
    user-select: none;
}

.hello-language strong {
    font-weight: 500;
    color: var(--na-highlight);
    line-height: 0;
    font-style: normal;
    letter-spacing: -0.2vw;
}

@media (max-width: 1700px) {

    .hello-language {
        font-size: 8vw;
        min-width: max-content;
        letter-spacing: -0.3vw;
    }

    .continue-btn {
        top: 58%;
    }

    #welcome-animate {
        font-size: 6vw;
    }

}

@media (max-width: 1280px) {

    .hello-language {
        font-size: 9.5vw;
        min-width: max-content;
        letter-spacing: -0.3vw;
    }

    #welcome-animate {
        font-size: 7vw;
    }

}

@media (max-width: 768px) {

    .hello-language {
        font-size: 14vw;
        min-width: max-content;
        letter-spacing: -0.4vw;  
    }

    .continue-btn {
        top: 65%;
    }

    .continue-btn {
        font-size: 3vw;
    }

}

@media (max-width: 420px) {

    .hello-language {
        font-size: 15vw;
    }

    .continue-btn {
        top: 60%;
        font-size: 4.5vw;
    }

}