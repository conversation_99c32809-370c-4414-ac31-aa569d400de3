const barFill = document.getElementById('bar-fill');
const text = document.getElementById('moniterDisplay');
const btnClear = document.getElementById('btn-clear');
const MAX = 5 * 1024 * 1024;

let cachedSize = 0;
let cacheValid = false;

const calcLocalStorageSize = () => {
    if (cacheValid) return cachedSize;
    let size = 0;
    for (let i = 0; i < localStorage.length; i++) {
        const k = localStorage.key(i);
        const v = localStorage.getItem(k);
        size += (k.length + v.length) * 2;
    }
    cachedSize = size;
    cacheValid = true;
    return size;
}

const fmtBytes = (b) => {
    if (b < 1024) return b + 'B';
    if (b < 1024 * 1024) return (b / 1024).toFixed(1) + 'KB';
    return (b / (1024 * 1024)).toFixed(2) + 'MB';
}

let scheduled = false;
const updateUI = () => {   
    if (scheduled) return;
    scheduled = true;
    requestAnimationFrame(() => {
        const used = calcLocalStorageSize();
        const pct = Math.min(used / MAX * 100, 100);
        barFill.style.width = pct + '%';
        if (pct > 90) barFill.style.background = '#ff4d4d';
        else if (pct > 70) barFill.style.background = '#ffd166';
        else barFill.style.background = '#00b4d8';
        text.textContent = `${fmtBytes(used)} / 5MB`;
        scheduled = false;
    });
}

const invalidateCacheAndUpdate = () => {
    cacheValid = false;
    updateUI();
}

const originalSetItem = localStorage.setItem;
const originalRemoveItem = localStorage.removeItem;
const originalClear = localStorage.clear;

localStorage.setItem = function (key, value) {
    originalSetItem.call(this, key, value);
    invalidateCacheAndUpdate(); 
};

localStorage.removeItem = function (key) {
    originalRemoveItem.call(this, key);
    invalidateCacheAndUpdate(); 
};

// Override localStorage.clear
localStorage.clear = function () {
    originalClear.call(this);
    invalidateCacheAndUpdate(); 
};

window.addEventListener('storage', function (e) {
    invalidateCacheAndUpdate();
});

let lastKnownSize = 0;
function pollForChanges() {
    const currentSize = calcLocalStorageSize();
    if (currentSize !== lastKnownSize) {
        lastKnownSize = currentSize;
        updateUI();
    }
}

setInterval(pollForChanges, 1000);

const observer = new MutationObserver(function (mutations) {
    let shouldUpdate = false;
    mutations.forEach(function (mutation) {
        if (mutation.type === 'childList') {
            mutation.addedNodes.forEach(function (node) {
                if (node.nodeType === Node.ELEMENT_NODE &&
                    (node.tagName === 'SCRIPT' || node.hasAttribute('data-storage'))) {
                    shouldUpdate = true;
                }
            });
        }
    });

    if (shouldUpdate) {
        setTimeout(invalidateCacheAndUpdate, 100);
    }
});

observer.observe(document.body, {
    childList: true,
    subtree: true
});

btnClear.onclick = () => {
    localStorage.clear(); 
}

window.addEventListener('beforeunload', function () {
    invalidateCacheAndUpdate();
});

window.addEventListener('focus', function () {
    invalidateCacheAndUpdate();
});

invalidateCacheAndUpdate();

// debug node tick true to enable
const DEBUG = false; 

if (DEBUG) {
    const originalLog = console.log;
    localStorage.setItem = function (key, value) {
        originalLog(`[Storage Monitor] SET: ${key} = ${value.substring(0, 50)}...`);
        originalSetItem.call(this, key, value);
        invalidateCacheAndUpdate();
    };

    localStorage.removeItem = function (key) {
        originalLog(`[Storage Monitor] REMOVE: ${key}`);
        originalRemoveItem.call(this, key);
        invalidateCacheAndUpdate();
    };

    localStorage.clear = function () {
        originalLog(`[Storage Monitor] CLEAR ALL`);
        originalClear.call(this);
        invalidateCacheAndUpdate();
    };
}

