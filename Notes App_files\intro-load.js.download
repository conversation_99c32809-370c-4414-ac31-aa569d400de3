window.resetWelcome = function () {
    localStorage.removeItem('hasVisited');
    location.reload();
    console.log('Welcome screen reset - page will reload');
};

function closeWelcome() {
    const overlay = document.getElementById('welcomeOverlay');
    overlay.classList.add('hidden');
    localStorage.setItem('hasVisited', 'true');

    setTimeout(() => {
        overlay.style.display = 'none';
    }, 600);
}

function checkFirstVisit() {
    if (localStorage.getItem('hasVisited')) {
        document.getElementById('welcomeOverlay').style.display = 'none';
    }
}

document.addEventListener('DOMContentLoaded', () => {
    checkFirstVisit();

    const introLoad = document.getElementById('introLoad');
    if (introLoad) {
        // if user enter for the first time loading delay to 1200 else 300ms
        const isFirstVisit = !localStorage.getItem('hasVisited');
        const loadingDelay = isFirstVisit ? 2000 : 200;

        setTimeout(() => {
            introLoad.classList.add('hidden');
            introLoad.addEventListener('transitionend', () => {
                introLoad.remove();
            });
        }, loadingDelay);
    }
});