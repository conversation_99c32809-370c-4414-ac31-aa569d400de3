/**
 * Event Handlers for Notes App
 * Converts all onclick attributes to proper event listeners
 */

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    
    // Welcome screen - Continue button
    const continueBtn = document.getElementById('continue-btn');
    if (continueBtn) {
        continueBtn.addEventListener('click', function() {
            if (typeof closeWelcome === 'function') {
                closeWelcome();
            }
        });
    }

    // Sidebar - Hide sidebar button (SVG icon)
    const hideSidebarBtn = document.getElementById('hideSidebarBtn');
    if (hideSidebarBtn) {
        hideSidebarBtn.addEventListener('click', function() {
            if (typeof hideSidebar === 'function') {
                hideSidebar();
            }
        });
    }

    // Background blur - Hide sidebar when clicked
    const backgroundBlur = document.getElementById('background-blur');
    if (backgroundBlur) {
        backgroundBlur.addEventListener('click', function() {
            if (typeof hideSidebar === 'function') {
                hideSidebar();
            }
        });
    }

    // Create file buttons
    const createFileBtn = document.getElementById('createFileBtn');
    if (createFileBtn) {
        createFileBtn.addEventListener('click', function() {
            if (typeof createFile === 'function') {
                createFile();
            }
        });
    }

    // Export file button (note: this button has id="exportFileBtn" but should call exportFile function)
    const exportFileBtn = document.getElementById('exportFileBtn');
    if (exportFileBtn) {
        exportFileBtn.addEventListener('click', function() {
            if (typeof createFile === 'function') {
                // Based on the HTML, this button was calling createFile(), not exportFile()
                // This might be a bug in the original code, but maintaining the same behavior
                createFile();
            }
        });
    }

    // Save file button
    const saveBtn = document.getElementById('saveBtn');
    if (saveBtn) {
        saveBtn.addEventListener('click', function() {
            if (typeof saveFile === 'function') {
                saveFile();
            }
        });
    }

    // Export file button (actual export functionality)
    const exportBtn = document.getElementById('exportBtn');
    if (exportBtn) {
        exportBtn.addEventListener('click', function() {
            if (typeof exportFile === 'function') {
                exportFile();
            }
        });
    }

    // Toggle sidebar button
    const toggleSidebarBtn = document.getElementById('toggleSidebar');
    if (toggleSidebarBtn) {
        toggleSidebarBtn.addEventListener('click', function() {
            if (typeof toggleSidebar === 'function') {
                toggleSidebar();
            }
        });
    }

    // Modal - Cancel button
    const cancelCreateFileBtn = document.getElementById('cancelCreateFileBtn');
    if (cancelCreateFileBtn) {
        cancelCreateFileBtn.addEventListener('click', function() {
            if (typeof closeModal === 'function') {
                closeModal('createFileModal');
            }
        });
    }

    // Modal - Confirm create file button
    const confirmCreateFileBtn = document.getElementById('confirmCreateFileBtn');
    if (confirmCreateFileBtn) {
        confirmCreateFileBtn.addEventListener('click', function() {
            if (typeof confirmCreateFile === 'function') {
                confirmCreateFile();
            }
        });
    }

    console.log('Event handlers initialized successfully');
});
